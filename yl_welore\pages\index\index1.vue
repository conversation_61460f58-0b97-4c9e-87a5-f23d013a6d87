<template>
    <!-- 首页 -->
    <view style="background-color: #ffffff; padding-bottom: 200rpx; min-height: 1200rpx">
        <block v-for="(item, dataListindex) in new_list" :key="dataListindex">
            <view style="position: relative">
                <image
                    v-if="item.top_time"
                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/home_zhang.png'"
                    style="opacity: 0.6; width: 70px; height: 70px; position: absolute; right: 10%; top: 20%; z-index: 200"
                ></image>
                <view class="cu-list menu-avatar">
                    <view class="cu-item">
                        <view
                            @tap="home_url"
                            data-k="1"
                            :data-user_id="item.user_id"
                            class="cu-avatar round eight"
                            :style="'background-image:url(' + item.user_head_sculpture + ');'"
                        >
                            <view style="z-index: 100" :class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                            <image
                                v-if="item.user_id != 0"
                                class="now_level"
                                style="height: 96rpx; width: 96rpx; position: absolute; max-width: initial"
                                :src="item.avatar_frame"
                            ></image>
                        </view>
                        <view class="content flex-sub" style="left: 130rpx">
                            <view>
                                <view :class="item.user_id != 0 ? item.special : ''" style="font-size: 13px">
                                    {{ item.user_nick_name }}
                                </view>
                                <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx" :src="item.attr.attest.at_icon"></image>
                                <image
                                    v-if="item.user_vip == 1 && item.user_id != 0"
                                    :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                    style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                ></image>
                                <image v-if="item.user_id != 0" mode="heightFix" class="now_level" :src="item.level" style="height: 13px; margin-left: 3px"></image>
                                <image
                                    class="now_level"
                                    mode="heightFix"
                                    v-if="item.wear_merit && item.user_id != 0"
                                    :src="item.wear_merit"
                                    style="height: 13px; margin-left: 3px"
                                ></image>
                            </view>
                            <view class="text-gray text-sm flex">
                                <text
                                    v-if="item.check_qq == 'da' && item.user_id != 0"
                                    style="background-color: #9966ff; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                >
                                    {{ $state.diy.qq_name }}主
                                </text>
                                <text
                                    v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                    style="background-color: #4facfe; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                >
                                    管理
                                </text>
                                <text v-if="item.topping_time == 0 && order_time == 'fatie'" style="font-size: 12px; color: #888888">{{ item.adapter_time }}</text>
                                <text v-if="item.topping_time != 0" style="font-size: 12px; color: #888888">{{ item.adapter_time }}</text>
                                <text v-if="item.topping_time == 0 && order_time == 'huifu' && item.huifu_time != null" style="font-size: 12px; color: #888888">
                                    回复于{{ item.huifu_time }}
                                </text>
                            </view>
                        </view>
                    </view>
                    <view style="position: absolute; right: 14rpx; top: 20rpx">
                        <image
                            v-if="item.red == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/atk.png'"
                            style="width: 60rpx; height: 60rpx"
                        ></image>
                        <image
                            v-if="item.is_buy == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/ff.png'"
                            style="width: 60rpx; height: 60rpx"
                        ></image>
                        <image v-if="item.study_type == 3" :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/atb.png'" style="width: 60rpx; height: 60rpx"></image>
                    </view>
                </view>
            </view>

            <view style="padding: 0px 20px 0px 15px" v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 4 || item.study_type == 5 || item.study_type == 6">
                <view
                    v-if="item.gambit_id"
                    @tap="gambit_list"
                    :data-id="item.gambit_id"
                    style="font-weight: 300; display: inline-block; background-color: #ededed; border-radius: 20px; padding: 2px 10px 2px 2px; font-size: 12px; margin-bottom: 5px"
                >
                    <image style="width: 15px; height: 15px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                    <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                </view>
                <view
                    @tap="home_url"
                    data-k="3"
                    :data-index="dataListindex"
                    :data-type="item.study_type"
                    :data-id="item.id"
                    class=""
                    :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                >
                    <block v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 6">
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </block>
                    <block v-if="item.study_type == 4 || item.study_type == 5 || item.study_type == 6">
                        <rich-text class="text_num" :nodes="item.study_content"></rich-text>
                    </block>
                </view>
                <view
                    @tap="home_url"
                    data-k="3"
                    :data-index="dataListindex"
                    :data-type="item.study_type"
                    :data-id="item.id"
                    v-if="item.image_part.length > 0"
                    class="flex-sub padding-top col-1"
                >
                    <!-- 1 -->
                    <view v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <image :lazy-load="true" :src="img" style="border-radius: 3px; width: 100%; height: 400rpx" mode="heightFix"></image>
                    </view>
                    <!-- 1 -->
                    <!-- 2 -->
                    <view style="width: 50%; float: left; text-align: center" v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <image
                            :lazy-load="true"
                            v-if="img_index == 0"
                            :src="img"
                            style="border-radius: 3px 0px 0px 3px; height: 180px; width: 100%; padding-right: 2px"
                            mode="aspectFill"
                        ></image>

                        <image
                            :lazy-load="true"
                            v-if="img_index == 1"
                            :src="img"
                            style="border-radius: 0px 3px 3px 0px; height: 180px; width: 100%; padding-left: 2px"
                            mode="aspectFill"
                        ></image>
                    </view>
                    <!-- 2 -->
                    <!-- 3 -->
                    <block v-if="item.image_part.length == 3" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <view style="width: 65%; float: left; text-align: center" v-if="img_index == 0">
                            <image :lazy-load="true" :src="img" style="border-radius: 3px 0px 0px 3px; width: 100%; height: 185px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 1">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 3px 0px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 2">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 0px 3px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                        </view>
                    </block>
                    <!-- 3 -->
                    <!-- 4 -->
                    <block v-if="item.image_part.length >= 4 && item.image_part.length < 9" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <view style="width: 54%; float: left; text-align: center" v-if="img_index == 0">
                            <image :lazy-load="true" :src="img" style="border-radius: 3px 0px 0px 3px; width: 100%; height: 190px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 46%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 1">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 3px 0px 0px; width: 100%; height: 120px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 23%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 2">
                            <image :lazy-load="true" :src="img" style="width: 100%; height: 67px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 23%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 3">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 0px 3px 0px; width: 100%; height: 67px" mode="aspectFill"></image>
                        </view>
                    </block>
                    <!-- 4 -->
                    <!-- 9 -->
                    <block v-if="item.image_part.length >= 9" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <view v-if="img_index < 9" style="width: 33%; float: left; text-align: center; padding-left: 3px">
                            <image :lazy-load="true" :src="img" style="width: 100%; height: 110px; border-radius: 3px" mode="aspectFill"></image>
                        </view>
                    </block>
                    <!-- 9 -->
                </view>
                <view style="clear: both; height: 0"></view>
                <!-- 投票 -->
                <view v-if="item.study_type == 4 || item.study_type == 5" class="vote-container">
                    <view class="vote-content">
                        <view
                            @tap.stop.prevent="home_url"
                            data-k="3"
                            :data-index="dataListindex"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            class="vote-title"
                        >
                            <view class="vote-type-badge">
                                <text v-if="item.study_type == 4" class="vote-type-text">单选</text>
                                <text v-if="item.study_type == 5" class="vote-type-text">多选</text>
                            </view>
                            <rich-text class="text_num vote-title-text" :nodes="item.study_title"></rich-text>
                        </view>
                        <view class="vote-options">
                            <view class="vote-option-wrapper" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                <view
                                    class="vote-option-item"
                                    @tap.stop.prevent="dian_option"
                                    :data-id="vo_item.id"
                                    :data-key="dataListindex"
                                    :data-index="vo_index"
                                >
                                    <view class="vote-option-content">
                                        <view class="vote-option-text">
                                            {{ vo_item.ballot_name }}
                                        </view>
                                        <view class="vote-option-right">
                                            <text
                                                v-if="voi_item == vo_item.id"
                                                class="vote-check-icon cuIcon-check"
                                                v-for="(voi_item, index) in item.vo_id"
                                                :key="index"
                                            ></text>
                                            <text v-if="item.is_vo_check > 0" class="vote-count">{{ vo_item.voters }}</text>
                                        </view>
                                    </view>
                                </view>

                                <view
                                    v-if="item.is_vo_check > 0"
                                    class="vote-progress-bg"
                                >
                                    <view class="vote-progress-bar" :style="'width:' + vo_item.ratio + '%;'"></view>
                                </view>
                            </view>
                            <view
                                @tap.stop.prevent="home_url"
                                data-k="3"
                                :data-index="dataListindex"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                v-if="item.vo.length > 3"
                                class="vote-more-options"
                            >
                                <text class="vote-more-text">查看全部选项</text>
                                <text class="cuIcon-right vote-more-icon"></text>
                            </view>
                        </view>
                    </view>
                    <view class="vote-info">
                        <view v-if="item.vote_deadline != '' && item.vote_deadline != 0 && item.vote_deadline != -1" class="vote-deadline">
                            <text class="vote-info-icon">⏰</text>
                            <text class="vote-info-text">截止时间：{{ item.vote_deadline }}</text>
                        </view>
                        <view v-if="item.vote_deadline == -1" class="vote-deadline expired">
                            <text class="vote-info-icon">⏰</text>
                            <text class="vote-info-text">投票已截止</text>
                        </view>
                        <view class="vote-footer">
                            <view class="vote-participants">
                                <text class="vote-info-icon">👥</text>
                                <text class="vote-info-text">参与人数：{{ item.vo_count }}</text>
                            </view>
                            <view>
                                <button
                                @tap.stop.prevent="vote_do"
                                :data-index="vo_index"
                                :data-key="dataListindex"
                                v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                class="vote-submit-btn"
                            >
                                立即投票
                            </button>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 投票 -->
                <!-- </navigator> -->
            </view>

            <view v-if="item.study_type == 1" class="weui-cells weui-cells_after-title">
                <view style="padding: 0rpx 10px 10px 20px">
                    <view
                        v-if="item.gambit_id"
                        @tap="gambit_list"
                        :data-id="item.gambit_id"
                        style="
                            font-weight: 300;
                            display: inline-block;
                            background-color: #ededed;
                            border-radius: 20px;
                            padding: 2px 10px 2px 2px;
                            font-size: 12px;
                            margin-bottom: 5px;
                        "
                    >
                        <image style="width: 15px; height: 15px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                        <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                    </view>
                    <view
                        @tap="home_url"
                        data-k="3"
                        :data-index="dataListindex"
                        :data-type="item.study_type"
                        :data-id="item.id"
                        :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                    >
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </view>
                </view>
                <view
                    style="
                        margin: 0 auto;
                        overflow: hidden;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 170rpx;
                        width: 90%;
                        background-color: #f6f7f7;
                        border: 1px solid #f0f0f0;
                        border-radius: 10rpx;
                    "
                >
                    <view
                        :style="
                            'background-image: url(' +
                            item.user_head_sculpture +
                            ');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
                        "
                    >
                        <view class="audioOpen" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                            <text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
                        </view>
                        <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                            <text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
                        </view>
                    </view>
                    <view style="width: 75%; padding: 20rpx">
                        <view style="display: flex; justify-content: space-between; align-items: center">
                            <view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ item.user_nick_name }}上传的音乐</view>
                            <view class="times">{{ item.starttime }}</view>
                        </view>
                        <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
                            <view style="font-size: 24rpx; color: #999">{{ item.user_nick_name }}</view>
                            <view>
                                <slider style="width: 170rpx" @change="sliderChange" block-size="12px" step="1" :value="item.offset" :max="item.max" selected-color="#4c9dee" />
                            </view>
                        </view>
                    </view>
                </view>
                <!-- </view> -->
                <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id" class="flex-sub padding-lr col-1">
                    <!-- 1 -->
                    <view v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <image :lazy-load="true" :src="img" style="border-radius: 3px; width: 100%; height: 450rpx" mode="heightFix"></image>
                    </view>
                    <!-- 1 -->
                    <!-- 2 -->
                    <view style="width: 50%; float: left; text-align: center" v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <image
                            :lazy-load="true"
                            v-if="img_index == 0"
                            :src="img"
                            style="border-radius: 3px 0px 0px 3px; height: 180px; width: 100%; padding-right: 2px"
                            mode="aspectFill"
                        ></image>

                        <image
                            :lazy-load="true"
                            v-if="img_index == 1"
                            :src="img"
                            style="border-radius: 0px 3px 3px 0px; height: 180px; width: 100%; padding-left: 2px"
                            mode="aspectFill"
                        ></image>
                    </view>
                    <!-- 2 -->
                    <!-- 3 -->
                    <block v-if="item.image_part.length == 3" v-for="(img, img_index) in item.image_part" :key="img_index">
                        <view style="width: 65%; float: left; text-align: center" v-if="img_index == 0">
                            <image :lazy-load="true" :src="img" style="border-radius: 3px 0px 0px 3px; width: 100%; height: 185px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 1">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 3px 0px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                        </view>

                        <view style="width: 35%; float: left; text-align: center; padding-left: 5px" v-if="img_index == 2">
                            <image :lazy-load="true" :src="img" style="border-radius: 0px 0px 3px 0px; width: 100%; height: 91px" mode="aspectFill"></image>
                        </view>
                    </block>
                    <!-- 3 -->
                </view>
            </view>

            <!-- 视频 -->

            <view v-if="item.study_type == 2">
                <view style="padding: 0rpx 10px 10px 20px">
                    <view
                        v-if="item.gambit_id"
                        @tap="gambit_list"
                        :data-id="item.gambit_id"
                        style="
                            font-weight: 300;
                            display: inline-block;
                            background-color: #ededed;
                            border-radius: 20px;
                            padding: 2px 10px 2px 2px;
                            font-size: 12px;
                            margin-bottom: 5px;
                        "
                    >
                        <image style="width: 15px; height: 15px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                        <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                    </view>
                    <view
                        @tap="home_url"
                        data-k="3"
                        :data-index="dataListindex"
                        :data-type="item.study_type"
                        :data-id="item.id"
                        :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                    >
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </view>
                </view>
                <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id">
                    <view v-if="item.image_part.length > 0" class="grid flex-sub col-1" style="position: relative">
                        <image :src="item.image_part[0]" mode="aspectFill" style="height: 190px; margin: 0 auto; border-radius: 5px"></image>
                        <text class="cuIcon-videofill lg bf" style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"></text>
                    </view>
                    <view
                        v-if="item.image_part.length == null || item.image_part.length == 0"
                        class="bg-black padding radius text-center shadow-blur"
                        style="position: relative; margin: 0 auto; width: 80%; height: 180px; z-index: 100; overflow: hidden; border-radius: 5px; font-size: 16px"
                    >
                        <text class="cuIcon-videofill lg text-white" style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"></text>
                    </view>
                </view>
            </view>

            <!-- 视频 -->

            <!-- 位置 -->

            <!-- 位置 -->

            <view style="clear: both; height: 0"></view>

            <!-- home=1 -->

            <view class="flex justify-between" style="margin: 10px 15px 10px 10px">
                <view class="flex justify-start">
                    <view class="flex margin-xs align-center">
                        <view class="cu-capsule round">
                            <view class="cu-tag bg-blue">
                                <text class="cicon-discover text-white" style="font-size: 12px"></text>
                            </view>
                            <view style="font-weight: 300; font-size: 11px" class="cu-tag line-blue" @tap="home_url" :data-id="item.tory_id" data-k="2">
                                {{ item.realm_name }}
                            </view>
                        </view>
                    </view>
                </view>
                <view class="flex justify-end">
                    <view v-if="ad_info.paper_browse_num_hide == 1" class="flex margin-xs align-center">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/kan.png'" style="width: 23px; height: 23px"></image>
                        <view class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 6rpx">
                            {{ item.study_heat }}
                        </view>
                    </view>
                    <view class="flex margin-xs align-center" @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')" :data-id="item.id" :data-key="dataListindex">
                        <image
                            :animation="item.animationData_zan"
                            v-if="item.is_info_zan == false"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/xiao_no.png'"
                            style="width: 40rpx; height: 40rpx"
                        ></image>
                        <image
                            :animation="item.animationData_zan"
                            v-if="item.is_info_zan == true"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/xiao.png'"
                            style="width: 40rpx; height: 40rpx"
                        ></image>
                        <view class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 15rpx">
                            {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}
                        </view>
                    </view>
                    <view @tap="home_pl" :data-id="item.id" :data-type="item.study_type" :data-key="dataListindex" class="flex margin-xs align-center">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/info_pl.png'" style="width: 40rpx; height: 40rpx"></image>
                        <view class="index_nav_name" style="color: #999999; font-size: 12px; margin-left: 15rpx">
                            {{ item.study_repount }}
                        </view>
                    </view>
                </view>
            </view>

            <!-- home=1 -->

            <!-- home=0 -->

            <!-- <view wx:if="{{home==0}}" class="flex" style="margin: 0px 15px 10px 15px;">

				<view class="flex-sub margin-xs align-center text-center">
					<image src='/yl_welore/style/icon/kan.png' style="width: 23px;height:23px;vertical-align: middle;"></image>
					<text class='index_nav_name' style='color:#999999;font-size:13px;margin-left:15rpx;'>{{item.study_heat}}</text>
				</view>
				<view class="flex-sub margin-xs align-center text-center" bindtap="{{item.is_buy==1?'':'add_zan'}}" data-id="{{item.id}}" data-key="{{dataListindex}}">
					<image animation='{{item.animationData_zan}}' wx:if="{{item.is_info_zan==false}}" src='/yl_welore/style/icon/xiao_no.png' style="width: 23px;height:23px;vertical-align: middle;"></image>
					<image animation='{{item.animationData_zan}}' wx:if="{{item.is_info_zan==true}}" src='/yl_welore/style/icon/xiao.png' style="width: 23px;height:23px;vertical-align: middle;"></image>
					<text class='index_nav_name' style='color:#999999;font-size:13px;margin-left:15rpx;'>{{item.info_zan_count_this>10000?item.info_zan_count:item.info_zan_count_this}}</text>
				</view>
				<view bindtap="home_pl" data-id='{{item.id}}' data-key='{{dataListindex}}' class="flex-sub margin-xs align-center text-center">
					<image src='/yl_welore/style/icon/info_pl.png' style="width: 23px;height:23px;vertical-align: middle;"></image>
					<text class='index_nav_name' style='color:#999999;font-size:13px;margin-left:15rpx;'>{{item.study_repount}}</text>
				</view>
		
		</view> -->

            <!-- home=0 -->

            <view style="width: 95%; height: 1px; background-color: #eeeeee; margin: 0 auto; border-radius: 5px"></view>

            <view style="padding: 30rpx" v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                <ad :unit-id="ad_info.adunit_id"></ad>
            </view>

            <view v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1" style="width: 100%; height: 5px; background-color: #f7f7fa"></view>
        </block>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
    <!-- 首页 -->
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>
<style scoped>
/* 投票容器样式 */
.vote-container {
    margin-top: 30rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(28, 187, 180, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.vote-container:hover {
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
    transform: translateY(-2rpx);
}

/* 投票内容区域 */
.vote-content {
    padding: 40rpx 30rpx 30rpx;
}

/* 投票标题样式 */
.vote-title {
    margin-bottom: 30rpx;
    cursor: pointer;
}

.vote-type-badge {
    display: inline-block;
    background: linear-gradient(135deg, #1cbbb4 0%, #00d4aa 100%);
    color: #ffffff;
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(28, 187, 180, 0.3);
}

.vote-type-text {
    color: #ffffff;
}

.vote-title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #2d3436;
    line-height: 1.5;
    display: block;
}

/* 投票选项区域 */
.vote-options {
    margin-bottom: 20rpx;
}

.vote-option-wrapper {
    position: relative;
    margin-bottom: 24rpx;
}

.vote-option-wrapper:last-child {
    margin-bottom: 0;
}

.vote-option-item {
    background: linear-gradient(135deg, #ffffff 0%, #f1f9ff 100%);
    border-radius: 16rpx;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    z-index: 2;
    overflow: hidden;
}

.vote-option-item:active {
    transform: translateY(0);
}

.vote-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 24rpx;
    position: relative;
    z-index: 3;
}

.vote-option-text {
    flex: 1;
    font-size: 28rpx;
    color: #2d3436;
    font-weight: 500;
    line-height: 1.4;
    margin-right: 20rpx;
}

.vote-option-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.vote-check-icon {
    font-size: 32rpx;
    color: #00b894;
    font-weight: bold;
}

.vote-count {
    font-size: 26rpx;
    color: #636e72;
    font-weight: 600;
    background: rgba(28, 187, 180, 0.1);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
}

/* 进度条样式 */
.vote-progress-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16rpx;
    overflow: hidden;
    z-index: 1;
}

.vote-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    border-radius: 16rpx;
    transition: width 0.8s ease;
    opacity: 0.3;
}

/* 查看更多选项 */
.vote-more-options {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    text-align: center;
    margin-top: 16rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
}

.vote-more-options:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(253, 203, 110, 0.4);
}

.vote-more-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
}

.vote-more-icon {
    font-size: 24rpx;
    color: #ffffff;
}

/* 投票信息区域 */
.vote-info {
    background: rgba(116, 185, 255, 0.05);
    padding: 30rpx;
    border-top: 1px solid rgba(116, 185, 255, 0.1);
}

.vote-deadline {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    gap: 12rpx;
}

.vote-deadline.expired .vote-info-text {
    color: #e17055;
}

.vote-info-icon {
    font-size: 24rpx;
}

.vote-info-text {
    font-size: 26rpx;
    color: #636e72;
    font-weight: 500;
}

.vote-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vote-participants {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

/* 投票按钮 */
.vote-submit-btn {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: #ffffff;
    border: none;
    border-radius: 24rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 20rpx rgba(232, 67, 147, 0.3);
    line-height: 1;
}

.vote-submit-btn:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(232, 67, 147, 0.4);
}

.vote-submit-btn:active {
    transform: translateY(0);
}

.vote-submit-btn::after {
    display: none;
}
</style>
